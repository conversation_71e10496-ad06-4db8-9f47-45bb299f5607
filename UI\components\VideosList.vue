<template>
    <view class="container">
        <!-- 视频素材列表 -->
        <view class="media-list-container">
            <!-- 视频卡片列表项 -->
            <view v-for="(media, index) in filteredMedia" :key="index" class="video-card"
                @click="viewMediaDetail(media)">
                <view class="video-card-content">
                    <view class="video-thumbnail">
                        <image :src="media.thumbnail" mode="aspectFill" class="video-thumbnail-image"></image>
                        <text class="video-duration">{{ media.duration }}</text>
                        <u-tag :text="getStatusText(media)" :type="getStatusType(media)" size="mini"
                            class="video-status"></u-tag>
                    </view>

                    <view class="video-info">
                        <text class="video-title">{{ media.title }}</text>
                        <text class="video-description" v-if="media.description">{{ media.description }}</text>
                        <view class="video-meta">
                            <view class="meta-item">
                                <text class="meta-icon">📅</text>
                                <text>{{ formatDate(media.uploadTime) }}</text>
                            </view>
                            <view class="meta-item">
                                <text class="meta-icon">👤</text>
                                <text>{{ media.uploader }}</text>
                            </view>
                            <view class="meta-item" v-if="media.fileSize">
                                <text class="meta-icon">📁</text>
                                <text>{{ formatFileSize(media.fileSize) }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 空状态 -->
            <u-empty v-if="filteredMedia.length === 0" mode="list" :text="`暂无${getStatusLabel(currentStatus)}视频`"
                iconSize="120" textSize="16" marginTop="100">
                <u-button type="primary" text="上传视频" @click="showUploadModal" size="normal" shape="round"></u-button>
            </u-empty>
        </view>

        <!-- 悬浮按钮组 -->
        <FloatingActionButton text="上传" type="primary" :initialPosition="{ right: 20, bottom: 180 }"
            @click="showUploadModal" />

    </view>
</template>

<script>

import FloatingActionButton from '@/components/FloatingActionButton.vue';
import { queryVideos } from "@/api/video.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    components: {
        FloatingActionButton
    },
    data () {
        return {
            mediaList: [],
            currentStatus: 'all'
        }
    },
    computed: {
        filteredMedia () {
            let result = this.mediaList;

            // 按状态筛选
            if (this.currentStatus !== 'all') {
                result = result.filter(media => media.status === this.currentStatus);
            }

            return result;
        }
    },
    created () {
        // 加载视频
        this.loadAllMedia();

        // 监听刷新事件
        uni.$on('refreshVideoList', () => {
            this.loadAllMedia();
        });
    },

    beforeDestroy () {
        // 移除事件监听
        uni.$off('refreshVideoList');
    },

    methods: {
        async loadAllMedia () {
            try {
                this.showLoading("加载中...");

                const response = await queryVideos({
                    page: 1,
                    pageSize: 1000
                });

                if (response.success && response.data) {
                    this.mediaList = response.data.items.map(video => ({
                        id: video.id,
                        title: video.title,
                        description: video.description,
                        thumbnail: this.buildCompleteFileUrl(video.coverUrl) || '/assets/images/video-cover.jpg',
                        duration: this.formatDuration(video.duration),
                        uploader: video.creatorName || video.createdBy || '管理员',
                        uploadTime: video.createTime,
                        status: video.status,
                        videoUrl: this.buildCompleteFileUrl(video.videoUrl),
                        fileSize: video.fileSize,
                        views: video.views || 0,
                        likes: video.likes || 0,
                        comments: video.comments || 0
                    }));
                } else {
                    console.error('获取视频列表失败:', response.message);
                    this.showError(response.message || "获取视频列表失败");
                    this.mediaList = [];
                }

                this.hideLoading();
            } catch (error) {
                console.error('加载视频列表失败:', error);
                this.hideLoading();
                this.showError("加载失败");
                this.mediaList = [];
            }
        },

        showUploadModal () {
            this.safeNavigateTo('/pages/admin/media/upload');
        },

        viewMediaDetail (media) {
            this.safeNavigateTo(`/pages/admin/media/detail?id=${media.id}`);
        },



        // 获取状态文本
        getStatusText (media) {
            const statusMap = {
                0: '已下架',
                1: '已上架',
                2: '处理失败',
                3: '压缩中'
            };
            return statusMap[media.status] || '未知状态';
        },

        // 获取状态类型（用于u-tag的type属性）
        getStatusType (media) {
            const typeMap = {
                0: 'error',    // 已下架
                1: 'success',  // 已上架
                2: 'error',    // 处理失败
                3: 'warning'   // 压缩中
            };
            return typeMap[media.status] || 'info';
        },

        // 获取状态标签文本
        getStatusLabel (status) {
            const labelMap = {
                'all': '',
                0: '已下架',
                1: '已上架',
                2: '处理失败',
                3: '压缩中'
            };
            return labelMap[status] || '';
        },

        // 格式化文件大小
        formatFileSize (bytes) {
            if (!bytes) return '';
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/index.scss';

.container {
    padding: 0;
    background-color: #f7f7f7;
}

/* 媒体列表容器 */
.media-list-container {
    padding: 20rpx;
    padding-top: 0;
}

/* === 视频卡片样式 === */
.video-card {
    background: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.video-card:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.video-card-content {
    display: flex;
    padding: 20rpx;
    gap: 20rpx;
}

.video-thumbnail {
    position: relative;
    width: 240rpx;
    height: 135rpx;
    border-radius: 12rpx;
    overflow: hidden;
    flex-shrink: 0;
}

.video-thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-duration {
    position: absolute;
    bottom: 8rpx;
    right: 8rpx;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
    font-size: 20rpx;
    font-weight: 500;
}

.video-status {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
}

.video-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.video-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.video-description {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.video-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
}

.meta-item {
    font-size: 24rpx;
    color: #999;
    display: flex;
    align-items: center;
    gap: 4rpx;
}

.meta-icon {
    font-size: 24rpx;
    opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .video-card-content {
        flex-direction: column;
        gap: 16rpx;
    }

    .video-thumbnail {
        width: 100%;
        height: 200rpx;
    }
}
</style>
