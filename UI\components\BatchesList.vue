<template>
    <view class="container">
        <!-- 批次状态筛选 -->
        <view class="tabs-container">
            <!-- 使用 u-tabs 组件 -->
            <u-tabs v-if="false" :list="batchStatusTabs" :current="currentTabIndex" @change="onTabChange"
                :scrollable="false" lineColor="#007AFF" :activeStyle="{ color: '#007AFF' }"
                :inactiveStyle="{ color: '#666666' }"></u-tabs>

            <!-- 备用方案：自定义选项卡 -->
            <view class="custom-tabs">
                <view v-for="(tab, index) in batchStatusTabs" :key="index"
                    :class="['custom-tab', currentTabIndex === index ? 'active' : '']" @tap="onTabChange(index)">
                    {{ tab.name }}
                </view>
            </view>
        </view>

        <!-- 批次列表 -->
        <view class="media-list-container">
            <!-- 批次卡片列表项 -->
            <view v-for="(batch, index) in filteredBatches" :key="index" class="batch-card"
                @click="viewBatchDetail(batch)">

                <!-- 批次卡片头部 -->
                <view class="batch-card-header">
                    <view class="batch-status-badge">{{ getBatchStatusText(batch) }}</view>
                    <view class="batch-id">批次 #{{ batch.batchId }}</view>
                    <text class="batch-title">{{ batch.title }}</text>
                    <view class="batch-time-info">{{ formatDateRange(batch.startTime, batch.endTime) }}</view>
                </view>

                <!-- 批次卡片内容 -->
                <view class="batch-card-content">
                    <text class="batch-description" v-if="batch.description">{{ batch.description }}</text>

                    <!-- 关键指标 -->
                    <view class="batch-metrics">
                        <view class="metric-item">
                            <text class="metric-number">{{ batch.currentOnline || 0 }}</text>
                            <text class="metric-label">当前观看</text>
                        </view>
                        <view class="metric-item">
                            <text class="metric-number">{{ batch.totalViews || 0 }}</text>
                            <text class="metric-label">总观看数</text>
                        </view>
                        <view class="metric-item">
                            <text class="metric-number">{{ batch.participants || 0 }}</text>
                            <text class="metric-label">参与人数</text>
                        </view>
                        <view class="metric-item">
                            <text class="metric-number">¥{{ batch.totalReward || 0 }}</text>
                            <text class="metric-label">奖励金额</text>
                        </view>
                    </view>

                    <!-- 视频预览 -->
                    <view class="batch-video-preview" v-if="batch.videos && batch.videos.length > 0">
                        <view class="batch-video-thumb">
                            <image v-if="batch.videos[0].cover" :src="batch.videos[0].cover" mode="aspectFill"></image>
                            <view v-else class="no-cover-placeholder">
                                <u-icon name="photo" size="16" color="#ccc"></u-icon>
                            </view>
                        </view>
                        <view class="batch-video-info">
                            <text class="batch-video-title">{{ batch.videos[0].title || '批次主视频' }}</text>
                            <text class="batch-video-duration">时长: {{ formatDuration(batch.videos[0].duration) }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 空状态 -->
            <u-empty v-if="filteredBatches.length === 0" mode="list" :text="`暂无${getCurrentStatusLabel()}批次`"
                iconSize="120" textSize="16" marginTop="100">
                <u-button type="primary" text="创建批次" @click="createNewBatch" size="normal" shape="round"></u-button>
            </u-empty>
        </view>
    </view>
</template>

<script>

import { queryBatches } from "@/api/batch.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    data () {
        return {
            batchList: [],
            batchStatusTabs: [
                { name: '全部', value: 'all' },
                { name: '进行中', value: 'active' },
                { name: '未开始', value: 'pending' },
                { name: '已结束', value: 'ended' }
            ],
            currentBatchStatus: 'all',
            currentTabIndex: 0
        }
    },
    computed: {
        filteredBatches () {
            let result = this.batchList;

            // 按状态筛选
            if (this.currentBatchStatus !== 'all') {
                result = result.filter(batch => batch.status === this.currentBatchStatus);
            }

            return result;
        }
    },
    created () {
        this.loadAllBatches();
    },
    methods: {
        async loadAllBatches () {
            try {
                this.showLoading("加载批次中...");

                const response = await queryBatches({
                    page: 1,
                    pageSize: 1000
                });

                if (response.success && response.data) {
                    this.batchList = response.data.items.map(batch => {
                        console.log('批次数据:', batch);
                        console.log('videoCoverUrl:', batch.videoCoverUrl);
                        const coverUrl = this.buildCompleteFileUrl(batch.videoCoverUrl);
                        console.log('处理后的封面URL:', coverUrl);

                        return {
                            id: batch.id,
                            batchId: batch.batchId,
                            title: batch.name || batch.title, // 优先使用name字段作为批次名称
                            description: batch.description,
                            startTime: batch.startTime,
                            endTime: batch.endTime,
                            status: this.calculateBatchStatus(batch),
                            participants: batch.participants || 0,
                            totalReward: batch.totalReward || 0,
                            videos: batch.videoId ? [{
                                id: batch.videoId,
                                title: batch.videoTitle,
                                cover: coverUrl,
                                duration: batch.videoDuration
                            }] : [],
                            createdAt: batch.createdAt
                        };
                    });
                } else {
                    console.error('获取批次列表失败:', response.message);
                    this.showError(response.message || "获取批次列表失败");
                    this.batchList = [];
                }

                this.hideLoading();
            } catch (error) {
                console.error('加载批次列表失败:', error);
                this.hideLoading();
                this.showError("加载失败");
                this.batchList = [];
            }
        },

        // 计算批次状态
        calculateBatchStatus (batch) {
            const now = new Date();
            const startTime = new Date(batch.startTime);
            const endTime = new Date(batch.endTime);

            if (now < startTime) {
                return 'pending'; // 未开始
            } else if (now > endTime) {
                return 'ended'; // 已结束
            } else {
                return 'active'; // 进行中
            }
        },

        // 选项卡切换
        onTabChange (index) {
            this.currentTabIndex = index;
            this.currentBatchStatus = this.batchStatusTabs[index].value;
        },

        // 格式化日期范围
        formatDateRange (startTime, endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);

            const formatDate = (date) => {
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const hour = date.getHours().toString().padStart(2, '0');
                const minute = date.getMinutes().toString().padStart(2, '0');
                return `${month}-${day} ${hour}:${minute}`;
            };

            return `${formatDate(start)} ~ ${formatDate(end)}`;
        },

        // 获取批次状态文本
        getBatchStatusText (batch) {
            const statusMap = {
                'pending': '未开始',
                'active': '进行中',
                'ended': '已结束',
                'paused': '已暂停'
            };
            return statusMap[batch.status] || '未知';
        },

        // 获取批次状态对应的 uview-plus 标签类型
        getBatchStatusType (batch) {
            // 首先检查是否已过期
            const now = new Date();
            const endTime = new Date(batch.endTime);
            const startTime = new Date(batch.startTime);

            if (now > endTime) {
                return 'error'; // 已结束
            }

            if (now < startTime) {
                return 'warning'; // 未开始
            }

            // 其他状态
            if (batch.status === 'paused') return 'warning';
            return 'success'; // 进行中
        },

        viewBatchDetail (batch) {
            if (!batch || !batch.id) {
                console.error('无效的批次数据，无法跳转');
                uni.showToast({
                    title: '无效的批次数据',
                    icon: 'none'
                });
                return;
            }
            const url = `/pages/admin/media/batch-detail?id=${batch.id}`;
            uni.navigateTo({
                url: url,
                fail: (err) => {
                    console.error('跳转到批次详情页失败:', err);
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    });
                }
            });
        },

        createNewBatch () {
            uni.showToast({
                title: '请先选择视频创建批次',
                icon: 'none'
            });
        },

        getCurrentStatusLabel () {
            const currentTab = this.batchStatusTabs[this.currentTabIndex];
            return currentTab.name === '全部' ? '' : currentTab.name;
        },

        onImageLoad (e) {
            console.log('图片加载成功:', e);
        },

        onImageError (e) {
            console.log('图片加载失败:', e);
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/index.scss';

.container {
    padding: 0;
    background-color: #f7f7f7;
}

/* 选项卡容器 */
.tabs-container {
    background-color: #fff;
    padding: 0 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

/* 自定义选项卡样式 */
.custom-tabs {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0;
}

.custom-tab {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 28rpx;
    color: #666666;
    position: relative;
    transition: all 0.3s ease;
}

.custom-tab.active {
    color: #007AFF;
    font-weight: 600;
}

.custom-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40rpx;
    height: 4rpx;
    background-color: #007AFF;
    border-radius: 2rpx;
}

/* 媒体列表容器 */
.media-list-container {
    padding: 20rpx;
    padding-top: 0;
}

/* === 批次卡片样式 === */
.batch-card {
    background: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.batch-card:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.batch-card-header {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    color: white;
    padding: 32rpx 40rpx;
    position: relative;
}

.batch-status-badge {
    position: absolute;
    top: 24rpx;
    right: 32rpx;
    padding: 8rpx 24rpx;
    border-radius: 40rpx;
    font-size: 22rpx;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.batch-id {
    font-size: 26rpx;
    opacity: 0.8;
    margin-bottom: 8rpx;
}

.batch-title {
    font-size: 36rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
    line-height: 1.3;
}

.batch-time-info {
    font-size: 26rpx;
    opacity: 0.9;
}

.batch-card-content {
    padding: 40rpx;
}

.batch-description {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 32rpx;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.batch-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
    gap: 32rpx;
    margin-bottom: 32rpx;
}

.metric-item {
    text-align: center;
    padding: 24rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
}

.metric-number {
    font-size: 40rpx;
    font-weight: 700;
    color: #007AFF;
    display: block;
}

.metric-label {
    font-size: 24rpx;
    color: #666;
    margin-top: 8rpx;
}

.batch-video-preview {
    display: flex;
    align-items: center;
    gap: 24rpx;
    padding: 24rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    margin-top: 24rpx;
}

.batch-video-thumb {
    width: 120rpx;
    height: 80rpx;
    border-radius: 12rpx;
    overflow: hidden;
    flex-shrink: 0;
}

.batch-video-thumb image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.batch-video-info {
    flex: 1;
}

.batch-video-title {
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 4rpx;
}

.batch-video-duration {
    font-size: 22rpx;
    color: #999;
}

/* 无封面占位符 */
.no-cover-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .batch-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
