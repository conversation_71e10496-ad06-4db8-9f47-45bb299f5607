<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频和批次卡片设计样例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7f7f7;
            padding: 20px;
            line-height: 1.5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 30px 0 20px 0;
            border-left: 4px solid #007AFF;
            padding-left: 12px;
        }

        /* === 视频卡片样式 === */
        .video-card {
            background: #fff;
            border-radius: 16px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .video-card-content {
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        .video-thumbnail {
            position: relative;
            width: 240px;
            height: 135px;
            border-radius: 12px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-duration {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .video-status {
            position: absolute;
            top: 8px;
            left: 8px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-published { background: #34C759; color: white; }
        .status-draft { background: #FF9500; color: white; }
        .status-processing { background: #007AFF; color: white; }

        .video-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .video-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }

        .video-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 12px;
        }

        .meta-item {
            font-size: 13px;
            color: #999;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .meta-icon {
            width: 14px;
            height: 14px;
            opacity: 0.7;
        }

        .video-stats {
            display: flex;
            gap: 20px;
            padding-top: 12px;
            border-top: 1px solid #f0f0f0;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #666;
        }

        .stat-number {
            font-weight: 600;
            color: #333;
        }

        /* === 批次卡片样式 === */
        .batch-card {
            background: #fff;
            border-radius: 16px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .batch-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .batch-card-header {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            color: white;
            padding: 16px 20px;
            position: relative;
        }

        .batch-status-badge {
            position: absolute;
            top: 12px;
            right: 16px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .batch-id {
            font-size: 13px;
            opacity: 0.8;
            margin-bottom: 4px;
        }

        .batch-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .batch-time-info {
            font-size: 13px;
            opacity: 0.9;
        }

        .batch-card-content {
            padding: 20px;
        }

        .batch-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }

        .batch-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .metric-item {
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-number {
            font-size: 20px;
            font-weight: 700;
            color: #007AFF;
            display: block;
        }

        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .batch-video-preview {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-top: 12px;
        }

        .batch-video-thumb {
            width: 60px;
            height: 40px;
            border-radius: 6px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .batch-video-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .batch-video-info {
            flex: 1;
        }

        .batch-video-title {
            font-size: 13px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .batch-video-duration {
            font-size: 11px;
            color: #999;
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .video-card-content {
                flex-direction: column;
                gap: 16px;
            }
            
            .video-thumbnail {
                width: 100%;
                height: 200px;
            }
            
            .batch-metrics {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="section-title">视频卡片设计</h1>
        
        <!-- 视频卡片示例 -->
        <div class="video-card">
            <div class="video-card-content">
                <div class="video-thumbnail">
                    <img src="https://picsum.photos/240/135?random=1" alt="视频缩略图">
                    <div class="video-duration">05:32</div>
                    <div class="video-status status-published">已发布</div>
                </div>
                <div class="video-info">
                    <h3 class="video-title">企业宣传片：创新科技引领未来发展</h3>
                    <p class="video-description">这是一个关于企业创新发展的宣传视频，展示了公司在科技领域的最新成果和未来规划，内容丰富精彩。</p>
                    <div class="video-meta">
                        <div class="meta-item">
                            <span>📅</span>
                            <span>2024-01-15 14:30</span>
                        </div>
                        <div class="meta-item">
                            <span>👤</span>
                            <span>张三</span>
                        </div>
                        <div class="meta-item">
                            <span>📁</span>
                            <span>25.6 MB</span>
                        </div>
                    </div>
                    <div class="video-stats">
                        <div class="stat-item">
                            <span>👁️</span>
                            <span class="stat-number">1,234</span>
                            <span>观看</span>
                        </div>
                        <div class="stat-item">
                            <span>👍</span>
                            <span class="stat-number">89</span>
                            <span>点赞</span>
                        </div>
                        <div class="stat-item">
                            <span>💬</span>
                            <span class="stat-number">23</span>
                            <span>评论</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="video-card">
            <div class="video-card-content">
                <div class="video-thumbnail">
                    <img src="https://picsum.photos/240/135?random=2" alt="视频缩略图">
                    <div class="video-duration">12:45</div>
                    <div class="video-status status-processing">处理中</div>
                </div>
                <div class="video-info">
                    <h3 class="video-title">产品介绍视频：新一代智能设备功能演示</h3>
                    <p class="video-description">详细介绍了新产品的各项功能特性，包括操作演示、技术参数说明等内容。</p>
                    <div class="video-meta">
                        <div class="meta-item">
                            <span>📅</span>
                            <span>2024-01-14 09:15</span>
                        </div>
                        <div class="meta-item">
                            <span>👤</span>
                            <span>李四</span>
                        </div>
                        <div class="meta-item">
                            <span>📁</span>
                            <span>156.8 MB</span>
                        </div>
                    </div>
                    <div class="video-stats">
                        <div class="stat-item">
                            <span>👁️</span>
                            <span class="stat-number">856</span>
                            <span>观看</span>
                        </div>
                        <div class="stat-item">
                            <span>👍</span>
                            <span class="stat-number">45</span>
                            <span>点赞</span>
                        </div>
                        <div class="stat-item">
                            <span>💬</span>
                            <span class="stat-number">12</span>
                            <span>评论</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h1 class="section-title">批次卡片设计</h1>
        
        <!-- 批次卡片示例 -->
        <div class="batch-card">
            <div class="batch-card-header">
                <div class="batch-status-badge">进行中</div>
                <div class="batch-id">批次 #B2024001</div>
                <h3 class="batch-title">春季产品发布会直播活动</h3>
                <div class="batch-time-info">2024-01-15 14:00 - 2024-01-15 18:00</div>
            </div>
            <div class="batch-card-content">
                <p class="batch-description">
                    本次直播活动将全面展示公司春季新品，包括产品介绍、技术演示、专家访谈等环节，预计观看人数超过5000人。
                </p>
                <div class="batch-metrics">
                    <div class="metric-item">
                        <span class="metric-number">2,847</span>
                        <div class="metric-label">当前观看</div>
                    </div>
                    <div class="metric-item">
                        <span class="metric-number">4,521</span>
                        <div class="metric-label">总观看数</div>
                    </div>
                    <div class="metric-item">
                        <span class="metric-number">156</span>
                        <div class="metric-label">互动次数</div>
                    </div>
                    <div class="metric-item">
                        <span class="metric-number">¥8,500</span>
                        <div class="metric-label">奖励金额</div>
                    </div>
                </div>
                <div class="batch-video-preview">
                    <div class="batch-video-thumb">
                        <img src="https://picsum.photos/60/40?random=3" alt="视频缩略图">
                    </div>
                    <div class="batch-video-info">
                        <div class="batch-video-title">春季新品发布会主视频</div>
                        <div class="batch-video-duration">时长: 2:15:30</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="batch-card">
            <div class="batch-card-header">
                <div class="batch-status-badge">已结束</div>
                <div class="batch-id">批次 #B2024002</div>
                <h3 class="batch-title">员工培训视频观看任务</h3>
                <div class="batch-time-info">2024-01-10 09:00 - 2024-01-12 17:00</div>
            </div>
            <div class="batch-card-content">
                <p class="batch-description">
                    面向全体员工的安全培训视频观看任务，包含消防安全、职业健康等多个主题的培训内容。
                </p>
                <div class="batch-metrics">
                    <div class="metric-item">
                        <span class="metric-number">0</span>
                        <div class="metric-label">当前观看</div>
                    </div>
                    <div class="metric-item">
                        <span class="metric-number">1,256</span>
                        <div class="metric-label">总观看数</div>
                    </div>
                    <div class="metric-item">
                        <span class="metric-number">89</span>
                        <div class="metric-label">互动次数</div>
                    </div>
                    <div class="metric-item">
                        <span class="metric-number">¥3,200</span>
                        <div class="metric-label">奖励金额</div>
                    </div>
                </div>
                <div class="batch-video-preview">
                    <div class="batch-video-thumb">
                        <img src="https://picsum.photos/60/40?random=4" alt="视频缩略图">
                    </div>
                    <div class="batch-video-info">
                        <div class="batch-video-title">企业安全培训合集</div>
                        <div class="batch-video-duration">时长: 45:20</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
